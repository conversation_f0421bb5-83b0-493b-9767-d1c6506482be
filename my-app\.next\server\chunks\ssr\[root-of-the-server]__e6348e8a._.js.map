{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n// Utility function to merge Tailwind CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format date utilities\nexport const formatDate = (date: Date | string, locale = 'en-US') => {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString(locale);\n};\n\nexport const formatDateTime = (date: Date | string, locale = 'en-US') => {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleString(locale);\n};\n\nexport const formatRelativeTime = (date: Date | string) => {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n\n  if (diffInSeconds < 60) return 'just now';\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n  return `${Math.floor(diffInSeconds / 86400)}d ago`;\n};\n\n// String utilities\nexport const capitalize = (str: string) => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const truncate = (str: string, length: number) => {\n  return str.length > length ? `${str.substring(0, length)}...` : str;\n};\n\nexport const slugify = (str: string) => {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n};\n\n// Number utilities\nexport const formatCurrency = (\n  amount: number,\n  currency = 'USD',\n  locale = 'en-US'\n) => {\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const formatNumber = (num: number, locale = 'en-US') => {\n  return new Intl.NumberFormat(locale).format(num);\n};\n\n// Array utilities\nexport const unique = <T>(array: T[]): T[] => {\n  return Array.from(new Set(array));\n};\n\nexport const groupBy = <T, K extends keyof T>(\n  array: T[],\n  key: K\n): Record<string, T[]> => {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n};\n\n// Object utilities\nexport const omit = <T extends Record<string, unknown>, K extends keyof T>(\n  obj: T,\n  keys: K[]\n): Omit<T, K> => {\n  const result = { ...obj };\n  keys.forEach(key => delete result[key]);\n  return result;\n};\n\nexport const pick = <T extends Record<string, unknown>, K extends keyof T>(\n  obj: T,\n  keys: K[]\n): Pick<T, K> => {\n  const result = {} as Pick<T, K>;\n  keys.forEach(key => {\n    if (key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return result;\n};\n\n// Validation utilities\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\n// Async utilities\nexport const sleep = (ms: number): Promise<void> => {\n  return new Promise(resolve => setTimeout(resolve, ms));\n};\n\nexport const debounce = <T extends (...args: never[]) => unknown>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n};\n\nexport const throttle = <T extends (...args: never[]) => unknown>(\n  func: T,\n  limit: number\n): ((...args: Parameters<T>) => void) => {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,aAAa,CAAC,MAAqB,SAAS,OAAO;IAC9D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC;AACpC;AAEO,MAAM,iBAAiB,CAAC,MAAqB,SAAS,OAAO;IAClE,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,cAAc,CAAC;AAChC;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;IACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IAC5E,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;AACpD;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,WAAW,CAAC,KAAa;IACpC,OAAO,IAAI,MAAM,GAAG,SAAS,GAAG,IAAI,SAAS,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG;AAClE;AAEO,MAAM,UAAU,CAAC;IACtB,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,MAAM,iBAAiB,CAC5B,QACA,WAAW,KAAK,EAChB,SAAS,OAAO;IAEhB,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ;QACnC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,KAAa,SAAS,OAAO;IACxD,OAAO,IAAI,KAAK,YAAY,CAAC,QAAQ,MAAM,CAAC;AAC9C;AAGO,MAAM,SAAS,CAAI;IACxB,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;AAC5B;AAEO,MAAM,UAAU,CACrB,OACA;IAEA,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAGO,MAAM,OAAO,CAClB,KACA;IAEA,MAAM,SAAS;QAAE,GAAG,GAAG;IAAC;IACxB,KAAK,OAAO,CAAC,CAAA,MAAO,OAAO,MAAM,CAAC,IAAI;IACtC,OAAO;AACT;AAEO,MAAM,OAAO,CAClB,KACA;IAEA,MAAM,SAAS,CAAC;IAChB,KAAK,OAAO,CAAC,CAAA;QACX,IAAI,OAAO,KAAK;YACd,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QACxB;IACF;IACA,OAAO;AACT;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,MAAM,QAAQ,CAAC;IACpB,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\nimport type { BaseComponentProps } from '@/types';\n\ninterface ButtonProps extends BaseComponentProps {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  loading?: boolean;\n  type?: 'button' | 'submit' | 'reset';\n  onClick?: () => void;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  className,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  type = 'button',\n  onClick,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n  \n  const variants = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700',\n    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',\n    outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n    ghost: 'hover:bg-gray-100',\n    destructive: 'bg-red-600 text-white hover:bg-red-700',\n  };\n\n  const sizes = {\n    sm: 'h-8 px-3 text-sm',\n    md: 'h-10 px-4 py-2',\n    lg: 'h-12 px-6 text-lg',\n  };\n\n  return (\n    <button\n      type={type}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"mr-2 h-4 w-4 animate-spin\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAYA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,QAAQ,EACf,OAAO,EACP,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/utils';\nimport type { BaseComponentProps } from '@/types';\n\ninterface InputProps extends BaseComponentProps {\n  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';\n  placeholder?: string;\n  value?: string;\n  defaultValue?: string;\n  disabled?: boolean;\n  required?: boolean;\n  error?: string;\n  label?: string;\n  id?: string;\n  name?: string;\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;\n  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;\n}\n\nconst Input: React.FC<InputProps> = ({\n  className,\n  type = 'text',\n  placeholder,\n  value,\n  defaultValue,\n  disabled = false,\n  required = false,\n  error,\n  label,\n  id,\n  name,\n  onChange,\n  onBlur,\n  onFocus,\n  ...props\n}) => {\n  const inputId = id || name;\n\n  return (\n    <div className=\"w-full\">\n      {label && (\n        <label\n          htmlFor={inputId}\n          className=\"block text-sm font-medium text-gray-700 mb-1\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      <input\n        type={type}\n        id={inputId}\n        name={name}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50',\n          error && 'border-red-500 focus:ring-red-500',\n          className\n        )}\n        placeholder={placeholder}\n        value={value}\n        defaultValue={defaultValue}\n        disabled={disabled}\n        required={required}\n        onChange={onChange}\n        onBlur={onBlur}\n        onFocus={onFocus}\n        {...props}\n      />\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n      )}\n    </div>\n  );\n};\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AACA;;;AAmBA,MAAM,QAA8B,CAAC,EACnC,SAAS,EACT,OAAO,MAAM,EACb,WAAW,EACX,KAAK,EACL,YAAY,EACZ,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,EAAE,EACF,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,OAAO,EACP,GAAG,OACJ;IACC,MAAM,UAAU,MAAM;IAEtB,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGrD,8OAAC;gBACC,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,wOACA,SAAS,qCACT;gBAEF,aAAa;gBACb,OAAO;gBACP,cAAc;gBACd,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACR,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;uCAEe", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/components/ui/index.ts"], "sourcesContent": ["export { default as But<PERSON> } from './Button';\nexport { default as Input } from './Input';\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/constants/index.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'My App',\n  description: 'A professional Next.js application',\n  version: '1.0.0',\n  author: 'Your Name',\n  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n} as const;\n\nexport const API_CONFIG = {\n  baseUrl: process.env.NEXT_PUBLIC_API_URL || '/api',\n  timeout: 10000,\n  retries: 3,\n} as const;\n\nexport const ROUTES = {\n  HOME: '/',\n  ABOUT: '/about',\n  CONTACT: '/contact',\n  LOGIN: '/auth/login',\n  REGISTER: '/auth/register',\n  DASHBOARD: '/dashboard',\n  PROFILE: '/profile',\n  SETTINGS: '/settings',\n} as const;\n\nexport const STORAGE_KEYS = {\n  AUTH_TOKEN: 'auth_token',\n  REFRESH_TOKEN: 'refresh_token',\n  USER_PREFERENCES: 'user_preferences',\n  THEME: 'theme',\n} as const;\n\nexport const VALIDATION_RULES = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  USERNAME_MIN_LENGTH: 3,\n  USERNAME_MAX_LENGTH: 30,\n} as const;\n\nexport const HTTP_STATUS = {\n  OK: 200,\n  CREATED: 201,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  INTERNAL_SERVER_ERROR: 500,\n} as const;\n\nexport const PAGINATION = {\n  DEFAULT_PAGE: 1,\n  DEFAULT_LIMIT: 10,\n  MAX_LIMIT: 100,\n} as const;\n\nexport const THEMES = {\n  LIGHT: 'light',\n  DARK: 'dark',\n  SYSTEM: 'system',\n} as const;\n\nexport const BREAKPOINTS = {\n  SM: '640px',\n  MD: '768px',\n  LG: '1024px',\n  XL: '1280px',\n  '2XL': '1536px',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;IACR,KAAK,6DAAmC;AAC1C;AAEO,MAAM,aAAa;IACxB,SAAS,4CAAmC;IAC5C,SAAS;IACT,SAAS;AACX;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;AACZ;AAEO,MAAM,eAAe;IAC1B,YAAY;IACZ,eAAe;IACf,kBAAkB;IAClB,OAAO;AACT;AAEO,MAAM,mBAAmB;IAC9B,aAAa;IACb,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;AACvB;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,SAAS;IACT,aAAa;IACb,cAAc;IACd,WAAW;IACX,WAAW;IACX,uBAAuB;AACzB;AAEO,MAAM,aAAa;IACxB,cAAc;IACd,eAAe;IACf,WAAW;AACb;AAEO,MAAM,SAAS;IACpB,OAAO;IACP,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/hooks/useLocalStorage.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\ntype SetValue<T> = T | ((val: T) => T);\n\nfunction useLocalStorage<T>(\n  key: string,\n  initialValue: T\n): [T, (value: SetValue<T>) => void] {\n  // State to store our value\n  const [storedValue, setStoredValue] = useState<T>(() => {\n    if (typeof window === 'undefined') {\n      return initialValue;\n    }\n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? JSON.parse(item) : initialValue;\n    } catch (error) {\n      console.error(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  });\n\n  // Return a wrapped version of useState's setter function that persists the new value to localStorage\n  const setValue = (value: SetValue<T>) => {\n    try {\n      // Allow value to be a function so we have the same API as useState\n      const valueToStore = value instanceof Function ? value(storedValue) : value;\n      setStoredValue(valueToStore);\n      \n      if (typeof window !== 'undefined') {\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\n      }\n    } catch (error) {\n      console.error(`Error setting localStorage key \"${key}\":`, error);\n    }\n  };\n\n  return [storedValue, setValue];\n}\n\nexport default useLocalStorage;\n"], "names": [], "mappings": ";;;AAAA;;AAIA,SAAS,gBACP,GAAW,EACX,YAAe;IAEf,2BAA2B;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;QAChD,wCAAmC;YACjC,OAAO;QACT;;;IAQF;IAEA,qGAAqG;IACrG,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,mEAAmE;YACnE,MAAM,eAAe,iBAAiB,WAAW,MAAM,eAAe;YACtE,eAAe;YAEf;;QAGF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACF;IAEA,OAAO;QAAC;QAAa;KAAS;AAChC;uCAEe", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nfunction useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n\nexport default useDebounce;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,YAAe,KAAQ,EAAE,KAAa;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/hooks/useToggle.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\nfunction useToggle(initialValue = false): [boolean, () => void, (value: boolean) => void] {\n  const [value, setValue] = useState<boolean>(initialValue);\n\n  const toggle = useCallback(() => {\n    setValue(prev => !prev);\n  }, []);\n\n  const setToggle = useCallback((newValue: boolean) => {\n    setValue(newValue);\n  }, []);\n\n  return [value, toggle, setToggle];\n}\n\nexport default useToggle;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,UAAU,eAAe,KAAK;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAE5C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,SAAS,CAAA,OAAQ,CAAC;IACpB,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,OAAO;QAAC;QAAO;QAAQ;KAAU;AACnC;uCAEe", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/hooks/useFetch.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport type { LoadingState } from '@/types';\n\ninterface UseFetchOptions {\n  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';\n  headers?: Record<string, string>;\n  body?: unknown;\n}\n\ninterface UseFetchReturn<T> {\n  data: T | null;\n  loading: boolean;\n  error: string | null;\n  status: LoadingState;\n  refetch: () => void;\n}\n\nfunction useFetch<T = unknown>(\n  url: string,\n  options: UseFetchOptions = {}\n): UseFetchReturn<T> {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [status, setStatus] = useState<LoadingState>('idle');\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setStatus('loading');\n      setError(null);\n\n      const response = await fetch(url, {\n        method: options.method || 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        body: options.body ? JSON.stringify(options.body) : undefined,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      setData(result);\n      setStatus('success');\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      setStatus('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (url) {\n      fetchData();\n    }\n  }, [url]);\n\n  const refetch = () => {\n    fetchData();\n  };\n\n  return { data, loading, error, status, refetch };\n}\n\nexport default useFetch;\n"], "names": [], "mappings": ";;;AAAA;;AAiBA,SAAS,SACP,GAAW,EACX,UAA2B,CAAC,CAAC;IAE7B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEnD,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,UAAU;YACV,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,MAAM,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,IAAI,IAAI;YACtD;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ;YACR,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK;YACP;QACF;IACF,GAAG;QAAC;KAAI;IAER,MAAM,UAAU;QACd;IACF;IAEA,OAAO;QAAE;QAAM;QAAS;QAAO;QAAQ;IAAQ;AACjD;uCAEe", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/hooks/index.ts"], "sourcesContent": ["export { default as useLocalStorage } from './useLocalStorage';\nexport { default as useDebounce } from './useDebounce';\nexport { default as useToggle } from './useToggle';\nexport { default as useFetch } from './useFetch';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/01projeto/my-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { useState } from 'react';\nimport { Button, Input } from '@/components/ui';\nimport { APP_CONFIG } from '@/constants';\nimport { useToggle } from '@/hooks';\n\nexport default function Home() {\n  const [name, setName] = useState('');\n  const [isLoading, toggleLoading] = useToggle(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    toggleLoading();\n\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    alert(`Hello, ${name || 'World'}!`);\n    toggleLoading();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <main className=\"max-w-4xl mx-auto text-center\">\n          {/* Header */}\n          <div className=\"mb-12\">\n            <Image\n              className=\"mx-auto mb-8 dark:invert\"\n              src=\"/next.svg\"\n              alt=\"Next.js logo\"\n              width={200}\n              height={42}\n              priority\n            />\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-4\">\n              Welcome to{' '}\n              <span className=\"text-blue-600\">{APP_CONFIG.name}</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n              A professional Next.js application with TypeScript, Tailwind CSS,\n              and modern development practices.\n            </p>\n          </div>\n\n          {/* Demo Form */}\n          <div className=\"bg-white rounded-lg shadow-lg p-8 mb-12 max-w-md mx-auto\">\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-6\">\n              Try Our Components\n            </h2>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <Input\n                label=\"Your Name\"\n                placeholder=\"Enter your name\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n              />\n              <Button\n                type=\"submit\"\n                loading={isLoading}\n                className=\"w-full\"\n              >\n                {isLoading ? 'Processing...' : 'Say Hello'}\n              </Button>\n            </form>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <div className=\"text-blue-600 text-3xl mb-4\">⚡</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Fast Development</h3>\n              <p className=\"text-gray-600\">\n                Built with Next.js 15, TypeScript, and modern tooling for rapid development.\n              </p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <div className=\"text-green-600 text-3xl mb-4\">🎨</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Beautiful UI</h3>\n              <p className=\"text-gray-600\">\n                Styled with Tailwind CSS and custom components for a professional look.\n              </p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-md\">\n              <div className=\"text-purple-600 text-3xl mb-4\">🔧</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Developer Experience</h3>\n              <p className=\"text-gray-600\">\n                ESLint, Prettier, Jest, and Husky configured for code quality.\n              </p>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button variant=\"primary\" size=\"lg\">\n              Get Started\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              View Documentation\n            </Button>\n          </div>\n          </main>\n\n        {/* Footer */}\n        <footer className=\"mt-16 pt-8 border-t border-gray-200\">\n          <div className=\"flex flex-wrap justify-center gap-8 text-sm text-gray-600\">\n            <a\n              className=\"flex items-center gap-2 hover:text-blue-600 transition-colors\"\n              href=\"https://nextjs.org/docs\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Image\n                aria-hidden\n                src=\"/file.svg\"\n                alt=\"File icon\"\n                width={16}\n                height={16}\n              />\n              Documentation\n            </a>\n            <a\n              className=\"flex items-center gap-2 hover:text-blue-600 transition-colors\"\n              href=\"https://github.com\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Image\n                aria-hidden\n                src=\"/window.svg\"\n                alt=\"Window icon\"\n                width={16}\n                height={16}\n              />\n              GitHub\n            </a>\n            <a\n              className=\"flex items-center gap-2 hover:text-blue-600 transition-colors\"\n              href=\"https://nextjs.org\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Image\n                aria-hidden\n                src=\"/globe.svg\"\n                alt=\"Globe icon\"\n                width={16}\n                height={16}\n              />\n              Next.js\n            </a>\n          </div>\n          <p className=\"text-center text-gray-500 mt-4\">\n            Built with ❤️ using Next.js, TypeScript, and Tailwind CSS\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,WAAW,cAAc,GAAG,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAE7C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB;QAEA,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,CAAC,OAAO,EAAE,QAAQ,QAAQ,CAAC,CAAC;QAClC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,UAAK;oCACJ,WAAU;oCACV,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;8CAEV,8OAAC;oCAAG,WAAU;;wCAAoD;wCACrD;sDACX,8OAAC;4CAAK,WAAU;sDAAiB,yHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;;8CAElD,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAO9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC,qKAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;sDAEzC,8OAAC,uKAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAOjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,8OAAC,uKAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;8BAOxC,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,KAAI;;sDAEJ,8OAAC,6HAAA,CAAA,UAAK;4CACJ,aAAW;4CACX,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;;;;;;wCACR;;;;;;;8CAGJ,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,KAAI;;sDAEJ,8OAAC,6HAAA,CAAA,UAAK;4CACJ,aAAW;4CACX,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;;;;;;wCACR;;;;;;;8CAGJ,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,KAAI;;sDAEJ,8OAAC,6HAAA,CAAA,UAAK;4CACJ,aAAW;4CACX,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;;;;;;wCACR;;;;;;;;;;;;;sCAIN,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;AAOxD", "debugId": null}}]}